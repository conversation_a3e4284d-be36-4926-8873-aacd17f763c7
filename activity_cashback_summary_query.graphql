# Activity Cashback Summary Query
# This query returns all the information needed for the Activity Cashback UI

query ActivityCashbackSummary {
  activityCashbackSummary {
    success
    message
    data {
      # Current ranking info
      currentLevel
      currentLevelName
      nextLevel
      nextLevelName
      
      # Progress calculation for progress bar
      currentScore
      totalScoreForNextLevel
      scoreRequiredToUpgrade
      progressPercentage
      
      # Trading volume (MEME only for now)
      accumulatedTradingVolumeUsd
      
      # Activity tracking
      activeLogonDays
      
      # Cashback information
      accumulatedCashbackUsd
      claimableCashbackUsd
      claimedCashbackUsd
      
      # Additional tier info for UI styling
      currentTierColor
      currentTierIcon
      nextTierColor
      nextTierIcon
    }
  }
}

# Example response structure:
# {
#   "data": {
#     "activityCashbackSummary": {
#       "success": true,
#       "message": "Summary data retrieved successfully",
#       "data": {
#         "currentLevel": 3,
#         "currentLevelName": "Lv3",
#         "nextLevel": 4,
#         "nextLevelName": "Lv4",
#         "currentScore": 2250,
#         "totalScoreForNextLevel": 4000,
#         "scoreRequiredToUpgrade": 1750,
#         "progressPercentage": 64.3,
#         "accumulatedTradingVolumeUsd": 969858.12,
#         "activeLogonDays": 15,
#         "accumulatedCashbackUsd": 18858.12,
#         "claimableCashbackUsd": 150.50,
#         "claimedCashbackUsd": 18707.62,
#         "currentTierColor": "#8B5CF6",
#         "currentTierIcon": "🔥",
#         "nextTierColor": "#F59E0B",
#         "nextTierIcon": "⭐"
#       }
#     }
#   }
# }
