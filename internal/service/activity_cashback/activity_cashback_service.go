package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ActivityCashbackService implements ActivityCashbackServiceInterface
type ActivityCashbackService struct {
	TaskManagementServiceInterface
	TierManagementServiceInterface
	TaskProgressServiceInterface
	CashbackClaimServiceInterface

	categoryRepo    activity_cashback.TaskCategoryRepositoryInterface
	tierBenefitRepo activity_cashback.TierBenefitRepositoryInterface
}

// NewActivityCashbackService creates a new ActivityCashbackService
func NewActivityCashbackService() ActivityCashbackServiceInterface {
	// Initialize repositories
	categoryRepo := activity_cashback.NewTaskCategoryRepository()
	taskRepo := activity_cashback.NewActivityTaskRepository()
	progressRepo := activity_cashback.NewUserTaskProgressRepository()
	tierInfoRepo := activity_cashback.NewUserTierInfoRepository()
	tierBenefitRepo := activity_cashback.NewTierBenefitRepository()

	claimRepo := activity_cashback.NewActivityCashbackClaimRepository()

	// Initialize services
	tierService := NewTierManagementService(tierInfoRepo, tierBenefitRepo)
	progressService := NewTaskProgressService(progressRepo, taskRepo)
	taskService := NewTaskManagementService(taskRepo, categoryRepo, progressRepo, tierService, progressService)
	claimService := NewCashbackClaimService(claimRepo)

	return &ActivityCashbackService{
		TaskManagementServiceInterface: taskService,
		TierManagementServiceInterface: tierService,
		TaskProgressServiceInterface:   progressService,
		CashbackClaimServiceInterface:  claimService,
		categoryRepo:                   categoryRepo,
		tierBenefitRepo:                tierBenefitRepo,
	}
}

// InitializeUserForActivityCashback initializes a user for the activity cashback system
func (s *ActivityCashbackService) InitializeUserForActivityCashback(ctx context.Context, userID uuid.UUID) error {
	// Create user tier info if not exists
	_, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			if _, err := s.CreateUserTierInfo(ctx, userID); err != nil {
				return fmt.Errorf("failed to create user tier info: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get user tier info: %w", err)
		}
	}

	// Initialize task progress for available tasks
	if err := s.RefreshUserTasks(ctx, userID); err != nil {
		return fmt.Errorf("failed to refresh user tasks: %w", err)
	}

	global.GVA_LOG.Info("User initialized for activity cashback", zap.String("user_id", userID.String()))
	return nil
}

// GetUserDashboard retrieves user dashboard data
func (s *ActivityCashbackService) GetUserDashboard(ctx context.Context, userID uuid.UUID) (*UserDashboard, error) {
	// Get user tier info
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user tier info: %w", err)
	}

	// Get current tier benefit
	tierBenefit, err := s.tierBenefitRepo.GetByTierLevel(ctx, tierInfo.CurrentTier)
	if err != nil {
		return nil, fmt.Errorf("failed to get tier benefit: %w", err)
	}

	// Get next tier and points needed
	nextTier, pointsToNextTier, err := s.GetNextTierRequirement(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get next tier requirement: %w", err)
	}

	// Get claimable cashback
	claimableCashback, err := s.GetClaimableCashback(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get claimable cashback: %w", err)
	}

	// Get recent claims
	recentClaims, err := s.GetUserClaims(ctx, userID, 5, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent claims: %w", err)
	}

	// Get user rank
	userRank, err := s.GetUserRank(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user rank", zap.Error(err))
		userRank = 0 // Default to 0 if failed
	}

	dashboard := &UserDashboard{
		UserTierInfo:      tierInfo,
		TierBenefit:       tierBenefit,
		NextTier:          nextTier,
		PointsToNextTier:  pointsToNextTier,
		ClaimableCashback: claimableCashback,
		RecentClaims:      recentClaims,
		UserRank:          userRank,
	}

	return dashboard, nil
}

// GetTaskCenter retrieves task center data
func (s *ActivityCashbackService) GetTaskCenter(ctx context.Context, userID uuid.UUID) (*TaskCenter, error) {
	// Get all categories with tasks
	categories, err := s.categoryRepo.GetActive(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}

	// Get user progress
	userProgress, err := s.GetUserTaskProgress(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user progress: %w", err)
	}

	// Create progress map for quick lookup
	progressMap := make(map[uuid.UUID]*model.UserTaskProgress)
	for i := range userProgress {
		progressMap[userProgress[i].TaskID] = &userProgress[i]
	}

	// Build categories with tasks and progress
	var categoriesWithTasks []TaskCategoryWithTasks
	for _, category := range categories {
		tasks, err := s.GetTasksByCategory(ctx, category.Name)
		if err != nil {
			global.GVA_LOG.Error("Failed to get tasks for category", zap.Error(err), zap.String("category", category.Name))
			continue
		}

		var tasksWithProgress []TaskWithProgress
		for _, task := range tasks {
			taskWithProgress := TaskWithProgress{
				Task:     task,
				Progress: progressMap[task.ID],
			}
			tasksWithProgress = append(tasksWithProgress, taskWithProgress)
		}

		categoryWithTasks := TaskCategoryWithTasks{
			Category: category,
			Tasks:    tasksWithProgress,
		}
		categoriesWithTasks = append(categoriesWithTasks, categoryWithTasks)
	}

	// Calculate today's stats
	today := time.Now().Truncate(24 * time.Hour)
	completedToday := 0
	pointsEarnedToday := 0

	for _, progress := range userProgress {
		if progress.LastCompletedAt != nil && progress.LastCompletedAt.Truncate(24*time.Hour).Equal(today) {
			completedToday++
			pointsEarnedToday += progress.Task.Points
		}
	}

	// Get streak tasks
	streakTasks, err := s.GetUserStreaks(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user streaks", zap.Error(err))
		streakTasks = []model.UserTaskProgress{}
	}

	taskCenter := &TaskCenter{
		Categories:        categoriesWithTasks,
		UserProgress:      userProgress,
		CompletedToday:    completedToday,
		PointsEarnedToday: pointsEarnedToday,
		StreakTasks:       streakTasks,
	}

	return taskCenter, nil
}

// GetActivityCashbackSummary retrieves optimized summary data for frontend UI
func (s *ActivityCashbackService) GetActivityCashbackSummary(ctx context.Context, userID uuid.UUID) (*ActivityCashbackSummary, error) {
	// Get user tier info
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user tier info: %w", err)
	}

	// Get current tier benefit
	currentTierBenefit, err := s.tierBenefitRepo.GetByTierLevel(ctx, tierInfo.CurrentTier)
	if err != nil {
		return nil, fmt.Errorf("failed to get current tier benefit: %w", err)
	}

	// Get next tier and points needed
	nextTier, pointsToNextTier, err := s.GetNextTierRequirement(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get next tier requirement: %w", err)
	}

	// Calculate progress percentage
	progressPercentage := 0.0
	var totalScoreForNextLevel *int
	var scoreRequiredToUpgrade *int
	var nextLevel *int
	var nextLevelName *string
	var nextTierColor *string
	var nextTierIcon *string

	if nextTier != nil {
		nextLevelInt := nextTier.TierLevel
		nextLevel = &nextLevelInt
		nextLevelName = &nextTier.TierName
		nextTierColor = nextTier.TierColor
		nextTierIcon = nextTier.TierIcon

		totalScoreForNextLevel = &nextTier.MinPoints
		scoreRequiredToUpgrade = &pointsToNextTier

		// Calculate progress percentage
		currentProgress := tierInfo.TotalPoints - currentTierBenefit.MinPoints
		totalNeeded := nextTier.MinPoints - currentTierBenefit.MinPoints
		if totalNeeded > 0 {
			progressPercentage = float64(currentProgress) / float64(totalNeeded) * 100.0
			if progressPercentage > 100.0 {
				progressPercentage = 100.0
			}
		}
	} else {
		// User is at max tier
		progressPercentage = 100.0
	}

	// Convert decimal values to float64 for GraphQL
	accumulatedTradingVolumeUSD := tierInfo.TradingVolumeUSD
	accumulatedCashbackUSD := tierInfo.CumulativeCashbackUSD
	claimableCashbackUSD := tierInfo.ClaimableCashbackUSD
	claimedCashbackUSD := tierInfo.ClaimedCashbackUSD

	summary := &ActivityCashbackSummary{
		// Current ranking info
		CurrentLevel:     tierInfo.CurrentTier,
		CurrentLevelName: currentTierBenefit.TierName,
		NextLevel:        nextLevel,
		NextLevelName:    nextLevelName,

		// Progress calculation
		CurrentScore:           tierInfo.TotalPoints,
		TotalScoreForNextLevel: totalScoreForNextLevel,
		ScoreRequiredToUpgrade: scoreRequiredToUpgrade,
		ProgressPercentage:     progressPercentage,

		// Trading volume (MEME only for now)
		AccumulatedTradingVolumeUSD: accumulatedTradingVolumeUSD,

		// Activity tracking
		ActiveLogonDays: tierInfo.ActiveDaysThisMonth,

		// Cashback information
		AccumulatedCashbackUSD: accumulatedCashbackUSD,
		ClaimableCashbackUSD:   claimableCashbackUSD,
		ClaimedCashbackUSD:     claimedCashbackUSD,

		// Additional tier info
		CurrentTierColor: currentTierBenefit.TierColor,
		CurrentTierIcon:  currentTierBenefit.TierIcon,
		NextTierColor:    nextTierColor,
		NextTierIcon:     nextTierIcon,
	}

	return summary, nil
}

// RefreshTaskList refreshes the task list for a user
func (s *ActivityCashbackService) RefreshTaskList(ctx context.Context, userID uuid.UUID) error {
	return s.RefreshUserTasks(ctx, userID)
}

// GetUserTaskListByType retrieves user task progress by type (progress only, no task data)
func (s *ActivityCashbackService) GetUserTaskListByType(ctx context.Context, userID uuid.UUID, taskType model.TaskType) ([]model.UserTaskProgress, error) {
	// Get user progress filtered by task type
	progress, err := s.GetUserTaskProgressByType(ctx, userID, taskType)
	if err != nil {
		return nil, fmt.Errorf("failed to get user task progress by type: %w", err)
	}

	return progress, nil
}
