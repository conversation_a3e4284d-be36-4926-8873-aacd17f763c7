package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
)

// GraphQL request structure
type GraphQLRequest struct {
	Query     string                 `json:"query"`
	Variables map[string]interface{} `json:"variables,omitempty"`
}

// GraphQL response structure
type GraphQLResponse struct {
	Data   interface{} `json:"data"`
	Errors []struct {
		Message string `json:"message"`
		Path    []interface{} `json:"path,omitempty"`
	} `json:"errors,omitempty"`
}

func main() {
	// GraphQL endpoint
	endpoint := "http://localhost:8080/graphql"
	
	// JWT token - you'll need to replace this with a valid token
	token := os.Getenv("JWT_TOKEN")
	if token == "" {
		fmt.Println("Please set JWT_TOKEN environment variable")
		fmt.Println("You can generate one using: make jwt-token")
		return
	}

	// Test query for the new ActivityCashbackSummary API
	query := `
		query {
			activityCashbackSummary {
				success
				message
				data {
					currentLevel
					currentLevelName
					nextLevel
					nextLevelName
					currentScore
					totalScoreForNextLevel
					scoreRequiredToUpgrade
					progressPercentage
					accumulatedTradingVolumeUsd
					activeLogonDays
					accumulatedCashbackUsd
					claimableCashbackUsd
					claimedCashbackUsd
					currentTierColor
					currentTierIcon
					nextTierColor
					nextTierIcon
				}
			}
		}
	`

	// Create GraphQL request
	reqBody := GraphQLRequest{
		Query: query,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		fmt.Printf("Error marshaling request: %v\n", err)
		return
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("Error creating request: %v\n", err)
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Error making request: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Error reading response: %v\n", err)
		return
	}

	// Parse response
	var gqlResp GraphQLResponse
	if err := json.Unmarshal(body, &gqlResp); err != nil {
		fmt.Printf("Error parsing response: %v\n", err)
		return
	}

	// Print results
	fmt.Printf("Status Code: %d\n", resp.StatusCode)
	fmt.Printf("Response:\n")
	
	if len(gqlResp.Errors) > 0 {
		fmt.Printf("Errors:\n")
		for _, err := range gqlResp.Errors {
			fmt.Printf("  - %s\n", err.Message)
		}
	}

	// Pretty print the data
	prettyData, err := json.MarshalIndent(gqlResp.Data, "", "  ")
	if err != nil {
		fmt.Printf("Error formatting data: %v\n", err)
		return
	}
	
	fmt.Printf("Data:\n%s\n", string(prettyData))
}
